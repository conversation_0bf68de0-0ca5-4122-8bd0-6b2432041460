# 可控硅特性分析与修复

## 📊 实测数据分析

基于你的功率计测量结果：

| 设置% | 实测功率 | 功率比例 | 问题分析 |
|-------|----------|----------|----------|
| 0-8%  | 0W       | 0%       | 死区，可控硅未导通 |
| 9%    | 300-400W | 25-33%   | 开始导通，功率跳跃 |
| 11%   | 700W     | 58%      | 非线性增长 |
| 13%   | 1200W    | 100%     | 完全导通！ |

## 🔍 问题根源

### 1. 可控硅导通特性
- **导通阈值**：约9%PWM占空比
- **非线性区域**：9-13%快速增长
- **饱和区域**：13%以上完全导通

### 2. PWM频率问题
- **10kHz高频**：可控硅无法正常关断
- **需要低频**：让可控硅有足够时间关断

## ✅ 修复方案

### 1. PWM频率调整
```cpp
#define FIR_FREQ 1  // 从10kHz改为1Hz
```

**原理**：
- 1Hz = 1秒周期
- 给可控硅充分的关断时间
- 避免高频开关导致的异常导通

### 2. 特性补偿算法
```cpp
if (i < 9) {
    // 死区：0-8%完全关闭
    pwm_linearization_table[i] = 0.0f;
} else if (i < 13) {
    // 过渡区：9-13%线性映射到0-60%功率
    float transition = (i - 9.0f) / 4.0f;
    pwm_linearization_table[i] = transition * 0.6f;
} else {
    // 控制区：13%以上使用平方根压制
    float excess = (i - 13.0f) / 87.0f;
    pwm_linearization_table[i] = 0.6f + sqrt(excess) * 0.4f;
}
```

### 3. 预期效果

| 设置% | 新算法系数 | 预期功率 | 改进效果 |
|-------|------------|----------|----------|
| 8%    | 0.000      | 0W       | 保持死区 |
| 9%    | 0.000      | 0W       | 延迟到10% |
| 10%   | 0.150      | 180W     | 温和起步 |
| 11%   | 0.300      | 360W     | 控制增长 |
| 12%   | 0.450      | 540W     | 平滑过渡 |
| 13%   | 0.600      | 720W     | 不再跳满 |
| 15%   | 0.671      | 805W     | 压制增长 |
| 20%   | 0.748      | 898W     | 渐进增长 |

## 🧪 测试验证

### 关键测试命令
```bash
# 可控硅特性测试
PID;TRIACTEST

# 手动验证关键点
PID;HEATERTEST,8     # 应该0W（死区）
PID;HEATERTEST,9     # 应该0W（延迟启动）
PID;HEATERTEST,10    # 应该约180W（温和起步）
PID;HEATERTEST,11    # 应该约360W（不是700W）
PID;HEATERTEST,13    # 应该约720W（不是1200W）
PID;HEATERTEST,0     # 关闭
```

### 成功标志
- **13%不再跳到1200W**
- **功率增长更平滑**
- **低功率区域更可控**

## 🔧 进一步调优

如果效果仍不理想：

### 调整死区范围
```bash
# 如果10%仍然太强，扩大死区
# 修改代码：if (i < 11) { ... }
```

### 调整过渡区间
```bash
# 如果13%仍然跳跃，扩大过渡区
# 修改代码：} else if (i < 15) { ... }
```

### 尝试更低频率
```bash
# 如果1Hz仍有问题，尝试更低频率
PID;PWMFREQ,0.5  # 0.5Hz = 2秒周期
```

## 📈 理论基础

### 可控硅PWM控制原理
1. **导通条件**：门极触发 + 正向电压
2. **关断条件**：电流降到维持电流以下
3. **高频问题**：关断时间不足导致异常导通
4. **低频优势**：充分的关断时间

### 1Hz PWM的优势
- **周期长**：1秒周期给足关断时间
- **精确控制**：占空比直接对应功率比例
- **避免谐振**：远离电网频率，避免干扰

现在测试修复效果，13%应该不再跳到满功率了！