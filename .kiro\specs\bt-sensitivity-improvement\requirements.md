# Requirements Document

## Introduction

本需求文档旨在改进咖啡烘焙机固件中BT（豆温）数据的灵敏度，使其能够更快速、更准确地响应温度变化，同时保持ET（风温）和ROR（升温速率）数据的现有滤波效果不变。通过优化BT的卡尔曼滤波参数，提升温度监控的实时性和精确性。

## Requirements

### Requirement 1

**User Story:** 作为咖啡烘焙师，我希望BT温度数据能够更灵敏地响应实际温度变化，以便我能够更精确地控制烘焙过程。

#### Acceptance Criteria

1. WHEN 豆温发生变化 THEN BT数据 SHALL 比当前实现更快速地反映温度变化
2. WHEN 调整BT滤波参数 THEN 系统 SHALL 保持温度读数的稳定性，避免过度噪声
3. WHEN BT灵敏度提高 THEN 系统 SHALL 确保温度曲线仍然平滑可读

### Requirement 2

**User Story:** 作为系统维护者，我希望ET和ROR数据保持现有的滤波效果，确保系统的其他功能不受影响。

#### Acceptance Criteria

1. WHEN 修改BT滤波参数 THEN ET滤波参数 SHALL 保持不变（Q=0.08, R=2.0）
2. WHEN 修改BT滤波参数 THEN ROR滤波参数 SHALL 保持不变（Q=0.08, R=2.0）
3. WHEN 系统运行 THEN ET和ROR数据 SHALL 保持与修改前相同的响应特性

### Requirement 3

**User Story:** 作为开发者，我希望滤波参数的修改是可配置的，便于后续调优和维护。

#### Acceptance Criteria

1. WHEN 定义BT滤波参数 THEN 参数 SHALL 在user_config.h中独立配置
2. WHEN 修改滤波参数 THEN 系统 SHALL 支持编译时配置，无需修改核心代码
3. WHEN 参数配置错误 THEN 系统 SHALL 使用安全的默认值并记录警告

### Requirement 4

**User Story:** 作为用户，我希望能够通过调试命令验证BT灵敏度改进的效果。

#### Acceptance Criteria

1. WHEN 发送调试命令 THEN 系统 SHALL 显示当前BT滤波参数和状态
2. WHEN 温度变化时 THEN 系统 SHALL 提供详细的滤波前后数据对比
3. WHEN 测试灵敏度 THEN 系统 SHALL 支持实时监控BT响应速度的改进效果