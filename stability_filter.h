// stability_filter.h
// 针对无过零检测系统的增强稳定性滤波器
#ifndef STABILITY_FILTER_H
#define STABILITY_FILTER_H

#include <Arduino.h>

// 增强稳定性滤波器
class EnhancedStabilityFilter {
private:
    float* history;
    int history_size;
    int index;
    float last_stable_value;
    unsigned long last_change_time;
    bool is_stable;
    
    // 稳定性参数
    const float STABILITY_THRESHOLD = 2.0f;  // 稳定性阈值
    const unsigned long STABILITY_TIMEOUT = 1000; // 1秒稳定检测
    const float DEAD_ZONE = 3.0f;  // 死区范围
    
public:
    EnhancedStabilityFilter(int size = 10);
    ~EnhancedStabilityFilter();
    
    float filter(float value);
    bool isStable();
    void reset();
    float getStableValue();
    
    // 设置滤波参数
    void setStabilityThreshold(float threshold);
    void setDeadZone(float dead_zone);
    void setStabilityTimeout(unsigned long timeout);
};

// 自适应死区控制器
class AdaptiveDeadZone {
private:
    float last_value;
    float dead_zone;
    float noise_level;
    bool first_run;
    
public:
    AdaptiveDeadZone(float initial_dead_zone = 5.0f);
    
    float apply(float value);
    void updateNoiseLevel(float new_value);
    void setDeadZone(float new_dead_zone);
    float getDeadZone();
};

// 功率波动抑制器
class PowerFluctuationSuppressor {
private:
    float last_output;
    int stability_counter;
    const int STABILITY_COUNT = 5;  // 连续5次相同值才更新
    
public:
    PowerFluctuationSuppressor();
    
    float suppress(float input);
    void reset();
};

#endif // STABILITY_FILTER_H