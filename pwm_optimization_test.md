# PWM控制优化测试指南

## 测试命令列表

### 基础功能测试

```bash
# 1. PWM线性度测试
PID;PWMTEST

# 2. 功率稳定性测试  
PID;STABILITYTEST

# 3. 不同功率级别测试
PID;HEATERTEST,10    # 10%功率
PID;HEATERTEST,25    # 25%功率
PID;HEATERTEST,50    # 50%功率
PID;HEATERTEST,75    # 75%功率
PID;HEATERTEST,90    # 90%功率
PID;HEATERTEST,0     # 关闭火力
```

### 高级参数调优测试

```bash
# 4. PWM频率优化测试
PID;PWMFREQ,8000     # 8kHz
PID;PWMFREQ,10000    # 10kHz (默认)
PID;PWMFREQ,15000    # 15kHz
PID;PWMFREQ,20000    # 20kHz

# 5. 平滑因子调整
PID;SMOOTHING,0.1    # 强平滑
PID;SMOOTHING,0.15   # 默认
PID;SMOOTHING,0.2    # 弱平滑

# 6. 功率变化率限制调整
PID;RATELIMIT,1      # 每周期最大1%变化
PID;RATELIMIT,2      # 每周期最大2%变化 (默认)
PID;RATELIMIT,5      # 每周期最大5%变化
```

### 综合系统测试

```bash
# 7. 系统响应速度测试
PID;RESPONSETEST

# 8. PID控制系统测试
PID;PIDTEST

# 9. 温度稳定性测试
PID;STABILITYTEST
```

## 预期测试结果

### PWM线性度测试结果示例
```
功率% | 线性化系数 | PWM值 | 实际占空比%
  0%  |    0.000   |    0  |    0.0%
 10%  |    0.158   |  647  |   15.8%
 20%  |    0.302   | 1237  |   30.2%
 30%  |    0.435   | 1781  |   43.5%
 40%  |    0.561   | 2297  |   56.1%
 50%  |    0.681   | 2789  |   68.1%
 60%  |    0.796   | 3259  |   79.6%
 70%  |    0.906   | 3710  |   90.6%
 80%  |    1.000   | 4095  |  100.0%
 90%  |    1.000   | 4095  |  100.0%
100%  |    1.000   | 4095  |  100.0%
```

### 功率稳定性测试结果示例
```
时间:    1s, 功率: 50%, 变化率: 0.12%
时间:    2s, 功率: 50%, 变化率: 0.08%
时间:    3s, 功率: 50%, 变化率: 0.05%
时间:    4s, 功率: 50%, 变化率: 0.03%
时间:    5s, 功率: 50%, 变化率: 0.02%
最终变化率: 0.02%
✅ 稳定性测试通过！功率输出稳定。
```

## 优化效果评估标准

### 线性度评估
- ✅ 优秀：线性化系数平滑过渡，无突变
- ⚠️  良好：偶有小幅跳变，整体平滑
- ❌ 需改进：频繁跳变或明显非线性

### 稳定性评估
- ✅ 优秀：变化率 < 0.5%
- ⚠️  良好：变化率 0.5% - 1.5%
- ❌ 需改进：变化率 > 1.5%

### 响应速度评估
- ✅ 优秀：功率变化响应时间 < 100ms
- ⚠️  良好：响应时间 100ms - 500ms
- ❌ 需改进：响应时间 > 500ms

## 故障排除

### 如果PWM线性度测试失败
1. 检查PWM频率设置是否合理 (8-20kHz)
2. 验证线性化查找表是否正确初始化
3. 确认PWM通道配置正确

### 如果稳定性测试失败
1. 增加平滑因子 (降低到0.1)
2. 减少功率变化率限制 (设为1%/周期)
3. 检查功率缓冲区是否工作正常

### 如果响应速度过慢
1. 减少平滑因子 (提高到0.2-0.3)
2. 增加功率变化率限制 (设为5%/周期)
3. 降低PWM频率到8-10kHz

## 最优参数推荐

基于测试结果，推荐以下参数组合：

### 标准配置（平衡性能）
- PWM频率: 10kHz
- 平滑因子: 0.15
- 变化率限制: 2%/周期

### 高稳定性配置
- PWM频率: 8kHz
- 平滑因子: 0.1
- 变化率限制: 1%/周期

### 高响应性配置
- PWM频率: 15kHz
- 平滑因子: 0.2
- 变化率限制: 3%/周期

根据你的具体需求选择合适的配置。