#ifndef FILTER_VALIDATION_H
#define FILTER_VALIDATION_H

#include "FilterLib.h"
#include <Arduino.h>

// 卡尔曼滤波参数范围定义
#define DEFAULT_Q_VALUE 0.08f
#define DEFAULT_R_VALUE 2.0f
#define MIN_Q_VALUE 0.01f
#define MAX_Q_VALUE 0.5f
#define MIN_R_VALUE 0.5f
#define MAX_R_VALUE 10.0f

// 滤波器验证函数声明
bool validateKalmanParams(float q, float r, const char* test_name);
void getSafeKalmanParams(float q, float r, float* safe_q, float* safe_r, const char* context);
void initializeFiltersWithValidation();
KalmanFilter* getBTKalmanFilter();
KalmanFilter* getETKalmanFilter();
KalmanFilter* getRORKalmanFilter();
void monitorFilterOutput(float input, float output, const char* filter_name);

#endif // FILTER_VALIDATION_H