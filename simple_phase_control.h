// simple_phase_control.h
// 简化的相位控制方案 - 解决火力不线性问题
#ifndef SIMPLE_PHASE_CONTROL_H
#define SIMPLE_PHASE_CONTROL_H

#include <Arduino.h>

// 简化的相位控制类
class SimplePhaseControl {
private:
    int target_power = 0;           // 目标功率百分比(0-100)
    int pwm_channel = 5;            // PWM通道
    
    // 线性化查找表 - 解决可控硅非线性问题
    float linearization_table[101]; // 0-100%功率对应的修正系数
    
    // 功率平滑控制
    int power_buffer[5] = {0};      // 功率缓冲区
    int buffer_index = 0;
    
public:
    SimplePhaseControl(int channel = 5);
    
    // 初始化相位控制
    void begin();
    
    // 设置目标功率(0-100%)
    void setPower(int power_percent);
    
    // 获取当前功率
    int getCurrentPower();
    
    // 主循环更新函数
    void update();
    
    // 功率线性化处理
    float linearizePower(int power_percent);
    
    // 计算触发延迟
    unsigned long calculateTriggerDelay(float power_percent);
    
    // 平滑功率变化
    int smoothPowerTransition(int new_power);
    
    // 重置控制器
    void reset();
    
    // 调试信息
    void printDebugInfo();
    
private:
    // 初始化线性化查找表
    void initializeLinearizationTable();
    
    // 计算实际PWM值
    int calculatePWMValue();
};

// 简化的功率稳定性监控类
class SimplePowerMonitor {
private:
    float power_history[10] = {0};  // 功率历史记录
    int history_index = 0;
    
public:
    // 添加功率数据点
    void addPowerData(float power);
    
    // 检查功率稳定性
    bool isPowerStable();
    
    // 获取功率变化率
    float getPowerVariation();
    
    // 重置监控
    void reset();
};

#endif // SIMPLE_PHASE_CONTROL_H