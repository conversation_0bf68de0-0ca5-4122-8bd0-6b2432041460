// simple_phase_control.cpp
// 简化的相位控制实现
#include "simple_phase_control.h"
#include <math.h>

SimplePhaseControl::SimplePhaseControl(int channel) {
    pwm_channel = channel;
    initializeLinearizationTable();
}

void SimplePhaseControl::begin() {
    Serial.println("[PHASE_CONTROL] 简化相位控制已初始化");
}

void SimplePhaseControl::initializeLinearizationTable() {
    // 创建线性化查找表，补偿可控硅的非线性特性
    for (int i = 0; i <= 100; i++) {
        if (i == 0) {
            linearization_table[i] = 0.0f;    // 0%功率
        } else if (i == 100) {
            linearization_table[i] = 1.0f;    // 100%功率
        } else {
            // 使用平方根函数改善低功率区域的线性度
            float linear_ratio = i / 100.0f;
            
            // 应用非线性修正
            if (i < 20) {
                // 低功率区域：使用平方根提升响应
                linearization_table[i] = sqrt(linear_ratio) * 0.8f;
            } else if (i > 80) {
                // 高功率区域：使用平方函数平滑过渡
                linearization_table[i] = 0.2f + 0.8f * linear_ratio * linear_ratio;
            } else {
                // 中功率区域：线性映射
                linearization_table[i] = linear_ratio;
            }
        }
    }
    
    Serial.println("[PHASE_CONTROL] 功率线性化查找表已初始化");
}

void SimplePhaseControl::setPower(int power_percent) {
    // 限制功率范围
    power_percent = constrain(power_percent, 0, 100);
    
    // 应用功率平滑
    int smoothed_power = smoothPowerTransition(power_percent);
    target_power = smoothed_power;
    
    Serial.printf("[PHASE_CONTROL] 设置功率: %d%% -> 平滑后: %d%%\n", 
                  power_percent, smoothed_power);
}

int SimplePhaseControl::getCurrentPower() {
    return target_power;
}

void SimplePhaseControl::update() {
    // 计算并输出PWM值
    int pwm_value = calculatePWMValue();
    ledcWriteChannel(pwm_channel, pwm_value);
}

unsigned long SimplePhaseControl::calculateTriggerDelay(float power_percent) {
    if (power_percent <= 0) {
        return 10000;  // 最大延迟，不触发
    }
    if (power_percent >= 100) {
        return 500;    // 最小延迟，立即触发
    }
    
    // 基于线性化功率计算延迟
    float linear_power = linearizePower(power_percent);
    unsigned long delay_us = 500 + (unsigned long)((1.0f - linear_power) * 9500);
    
    return constrain(delay_us, 500, 9500);
}

float SimplePhaseControl::linearizePower(int power_percent) {
    power_percent = constrain(power_percent, 0, 100);
    return linearization_table[power_percent];
}

int SimplePhaseControl::smoothPowerTransition(int new_power) {
    // 将新功率值添加到缓冲区
    power_buffer[buffer_index] = new_power;
    buffer_index = (buffer_index + 1) % 5;
    
    // 计算移动平均
    int sum = 0;
    for (int i = 0; i < 5; i++) {
        sum += power_buffer[i];
    }
    
    return sum / 5;
}

int SimplePhaseControl::calculatePWMValue() {
    if (target_power <= 0) {
        return 0;  // 0功率，输出低电平
    }
    
    if (target_power >= 100) {
        return 4095;  // 100%功率，输出高电平
    }
    
    // 使用线性化功率计算PWM值
    float linear_power = linearizePower(target_power);
    
    return (int)(linear_power * 4095);
}

void SimplePhaseControl::reset() {
    target_power = 0;
    
    // 清空功率缓冲区
    for (int i = 0; i < 5; i++) {
        power_buffer[i] = 0;
    }
    buffer_index = 0;
    
    Serial.println("[PHASE_CONTROL] 相位控制器已重置");
}

void SimplePhaseControl::printDebugInfo() {
    Serial.println("========== 相位控制调试信息 ==========");
    Serial.printf("目标功率: %d%%\n", target_power);
    Serial.printf("线性化功率: %.3f\n", linearizePower(target_power));
    Serial.printf("触发延迟: %lu微秒\n", calculateTriggerDelay(target_power));
    Serial.printf("PWM值: %d\n", calculatePWMValue());
    Serial.println("=====================================");
}

// SimplePowerMonitor 实现
void SimplePowerMonitor::addPowerData(float power) {
    power_history[history_index] = power;
    history_index = (history_index + 1) % 10;
}

bool SimplePowerMonitor::isPowerStable() {
    float variation = getPowerVariation();
    return variation < 2.0f;  // 2%的稳定性阈值
}

float SimplePowerMonitor::getPowerVariation() {
    float sum = 0;
    float mean = 0;
    
    // 计算平均值
    for (int i = 0; i < 10; i++) {
        sum += power_history[i];
    }
    mean = sum / 10.0f;
    
    // 计算标准差
    float variance = 0;
    for (int i = 0; i < 10; i++) {
        variance += pow(power_history[i] - mean, 2);
    }
    
    return sqrt(variance / 10.0f);
}

void SimplePowerMonitor::reset() {
    for (int i = 0; i < 10; i++) {
        power_history[i] = 0;
    }
    history_index = 0;
}