// 获取风力和功率电位器值----------------------------------------------------
/*
  该函数读取电位器值，已使用低通滤波死区过滤等优化.
*/
#include "get_pot.h"


int ADC_MAX = 4095;
const float DEAD_ZONE_THRESHOLD = 1.5f;  // 降低死区阈值，改善低电位器响应
// 限幅常量,默认0，最灵敏，如果设置为2则电位器值变化幅度为3以上才改变该值
#define POT_CHANGE 1

// 电位器滤波
LowPassFilter lpf_pot_FIR(FIR_FILTER);
LowPassFilter lpf_pot_FAN(FAN_FILTER);
LowPassFilter lpf_pot_LED(LED_FILTER);
static unsigned long pot_count = 0;

//  上次读取的电位器值
float prev_fir_pot = 0; // 上一次的 功率电位器 值
float prev_fan_pot = 0; // 上一次的 风力电位器 值
float prev_led_pot = 0; // 上一次的 LED电位器 值

extern bool isCommandControlActive;

void update_pot_value(int pin, float &prev_pot, int &level, float max_val, LowPassFilter &lpf)
{
  float raw_adc = lpf.update(analogRead(pin));
  float threshold = (operatingMode == SHOUDONG) ? 0 : DEAD_ZONE_THRESHOLD;
  if (raw_adc < threshold)
    raw_adc = 0;
  int pot_value = map(raw_adc, 0, ADC_MAX, 0, max_val);
  //Serial.printf("[FAN_CALC] raw_adc=%.1f ADC_MAX=%d max_val=%.1f pot_value=%d\n", raw_adc, ADC_MAX, max_val, pot_value);
  if (abs(prev_pot - pot_value) > POT_CHANGE)
  {
    prev_pot = pot_value;
    level = pot_value;
    //Serial.printf("[POT_DEBUG] pin:%d raw:%.1f calc:%d DEAD_ZONE=%.1f filtered=%.1f\n", pin, raw_adc, pot_value, DEAD_ZONE_THRESHOLD, lpf.getFilteredValue());
  }
}

void get_pot()
{
  if (millis() - pot_count >= POT_UPDATE_INTERVAL)
  {
    pot_count = millis();

    // 处理FIR电位器
    #if HEATER_CMD_PRIORITY
    if (operatingMode == SHOUDONG && fanSpeedLevel > HTR_CUTOFF_FAN_VAL && !isCommandControlActive)
#else
    if (operatingMode == SHOUDONG && fanSpeedLevel > HTR_CUTOFF_FAN_VAL)
#endif
    {
      update_pot_value(IN_FIR_POT, prev_fir_pot, heaterPowerLevel, FIR_MAX, lpf_pot_FIR);
    }

    // 读取风扇电位器的模拟值
    update_pot_value(IN_FAN_POT, prev_fan_pot, fanSpeedLevel, FAN_MAX, lpf_pot_FAN);

    // 读取LED控制电位器
    int level_LED;
    update_pot_value(IN_LED_POT, prev_led_pot, level_LED, LED_WHTE_MAX, lpf_pot_LED);
    whiteLedBrightness = (level_LED * LED_WHTE_MAX) / 100;
    yellowLedBrightness = (level_LED * LED_YELLOW_MAX) / 100;
  }
}
//-----------------------------------------------------------------------
