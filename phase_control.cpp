// phase_control.cpp
// 无过零检测系统的相位控制实现
#include "phase_control.h"
#include <Arduino.h>

PhaseControl::PhaseControl() {
    current_power = 0;
    target_power = 0;
    last_cycle_time = 0;
    use_random_phase = true;
    
    // 初始化功率缓冲区
    for (int i = 0; i < buffer_size; i++) {
        power_buffer[i] = 0;
    }
    buffer_index = 0;
}

void PhaseControl::setPower(int power) {
    target_power = constrain(power, 0, 100);
    
    // 应用移动平均滤波
    power_buffer[buffer_index] = target_power;
    buffer_index = (buffer_index + 1) % buffer_size;
    
    float filtered_power = 0;
    for (int i = 0; i < buffer_size; i++) {
        filtered_power += power_buffer[i];
    }
    filtered_power /= buffer_size;
    
    target_power = round(filtered_power);
}

int PhaseControl::getCurrentPower() {
    return current_power;
}

void PhaseControl::update() {
    unsigned long current_time = micros();
    
    // 简单的相位控制逻辑
    if (current_time - last_cycle_time >= cycle_duration) {
        last_cycle_time = current_time;
        
        // 平滑过渡到目标功率
        if (abs(current_power - target_power) > 1) {
            if (target_power > current_power) {
                current_power = min(current_power + 1, target_power);
            } else {
                current_power = max(current_power - 1, target_power);
            }
        }
    }
}

int PhaseControl::getPWMValue() {
    // 将功率百分比转换为相位延迟
    if (current_power <= 0) return 0;
    if (current_power >= 100) return 4095;
    
    // 计算相位延迟（无过零检测的近似方法）
    float phase_ratio = current_power / 100.0;
    
    // 添加随机化以避免固定模式
    if (use_random_phase) {
        float random_factor = (random(-5, 6) / 100.0);
        phase_ratio = constrain(phase_ratio + random_factor, 0.0, 1.0);
    }
    
    // 转换为PWM值（0-4095）
    int pwm_value = round(phase_ratio * 4095);
    
    // 确保最小输出
    if (pwm_value > 0 && pwm_value < 50) {
        pwm_value = 50;
    }
    
    return constrain(pwm_value, 0, 4095);
}

void PhaseControl::reset() {
    current_power = 0;
    target_power = 0;
    last_cycle_time = 0;
    
    for (int i = 0; i < buffer_size; i++) {
        power_buffer[i] = 0;
    }
    buffer_index = 0;
}

void PhaseControl::setRandomPhase(bool enable) {
    use_random_phase = enable;
}

// 移动平均滤波器实现
MovingAverageFilter::MovingAverageFilter(int filter_size) {
    size = filter_size;
    buffer = new float[size];
    index = 0;
    sum = 0;
    
    for (int i = 0; i < size; i++) {
        buffer[i] = 0;
    }
}

MovingAverageFilter::~MovingAverageFilter() {
    delete[] buffer;
}

float MovingAverageFilter::filter(float value) {
    sum -= buffer[index];
    buffer[index] = value;
    sum += value;
    index = (index + 1) % size;
    
    return sum / size;
}

void MovingAverageFilter::reset() {
    index = 0;
    sum = 0;
    for (int i = 0; i < size; i++) {
        buffer[i] = 0;
    }
}

// 功率稳定性控制器实现
bool PowerStabilizer::checkStability(int current_power) {
    if (last_power != current_power) {
        last_power = current_power;
        stable_start_time = millis();
        is_stable = false;
        return false;
    }
    
    if (millis() - stable_start_time >= STABILITY_TIMEOUT) {
        is_stable = true;
        return true;
    }
    
    return is_stable;
}

bool PowerStabilizer::isStable() {
    return is_stable;
}

void PowerStabilizer::reset() {
    last_power = -1;
    stable_start_time = 0;
    is_stable = false;
}