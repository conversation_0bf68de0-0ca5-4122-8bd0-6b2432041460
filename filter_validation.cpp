#include "filter_validation.h"
#include "get_temp.h"

// 全局滤波器实例
static KalmanFilter* btKalmanFilter = nullptr;
static KalmanFilter* etKalmanFilter = nullptr;
static KalmanFilter* rorKalmanFilter = nullptr;

// 参数验证函数
bool validateKalmanParams(float q, float r, const char* test_name) {
    if (q < MIN_Q_VALUE || q > MAX_Q_VALUE) {
        Serial.printf("[%s] 参数Q=%.3f超出范围[%.3f, %.3f]\n", 
                      test_name, q, MIN_Q_VALUE, MAX_Q_VALUE);
        return false;
    }
    
    if (r < MIN_R_VALUE || r > MAX_R_VALUE) {
        Serial.printf("[%s] 参数R=%.1f超出范围[%.1f, %.1f]\n", 
                      test_name, r, MIN_R_VALUE, MAX_R_VALUE);
        return false;
    }
    
    Serial.printf("[%s] 参数验证通过: Q=%.3f, R=%.1f\n", test_name, q, r);
    return true;
}

// 安全参数回退机制
void getSafeKalmanParams(float q, float r, float* safe_q, float* safe_r, const char* context) {
    *safe_q = constrain(q, MIN_Q_VALUE, MAX_Q_VALUE);
    *safe_r = constrain(r, MIN_R_VALUE, MAX_R_VALUE);
    
    if (*safe_q != q || *safe_r != r) {
        Serial.printf("[%s] 参数被约束: Q=%.3f->%.3f, R=%.1f->%.1f\n", 
                      context, q, *safe_q, r, *safe_r);
    }
}

// 初始化带验证的滤波器
void initializeFiltersWithValidation() {
    // 使用默认参数初始化滤波器
    btKalmanFilter = new KalmanFilter(DEFAULT_Q_VALUE, DEFAULT_R_VALUE);
    etKalmanFilter = new KalmanFilter(DEFAULT_Q_VALUE, DEFAULT_R_VALUE);
    rorKalmanFilter = new KalmanFilter(0.08f, 2.0f); // ROR使用固定参数
    
    Serial.println("滤波器初始化完成");
}

// 获取滤波器实例
KalmanFilter* getBTKalmanFilter() {
    if (btKalmanFilter == nullptr) {
        initializeFiltersWithValidation();
    }
    return btKalmanFilter;
}

KalmanFilter* getETKalmanFilter() {
    if (etKalmanFilter == nullptr) {
        initializeFiltersWithValidation();
    }
    return etKalmanFilter;
}

KalmanFilter* getRORKalmanFilter() {
    if (rorKalmanFilter == nullptr) {
        initializeFiltersWithValidation();
    }
    return rorKalmanFilter;
}

// 监控滤波器输出
void monitorFilterOutput(float input, float output, const char* filter_name) {
    if (isnan(output)) {
        Serial.printf("[%s] 警告: 输出为NaN\n", filter_name);
    } else if (output < -50 || output > 300) {
        Serial.printf("[%s] 警告: 输出超出正常范围: %.2f\n", filter_name, output);
    } else {
        Serial.printf("[%s] 正常: 输入=%.2f, 输出=%.2f\n", filter_name, input, output);
    }
}