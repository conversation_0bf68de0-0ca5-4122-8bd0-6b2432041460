// improved_phase_control.h
// 改进的可控硅相位控制方案 - 解决火力不线性问题
#ifndef IMPROVED_PHASE_CONTROL_H
#define IMPROVED_PHASE_CONTROL_H

#include <Arduino.h>
#include <driver/ledc.h>
#include <math.h>

// 交流电参数
#define AC_FREQUENCY 50                    // 中国电网频率50Hz
#define AC_HALF_PERIOD_US (1000000 / (AC_FREQUENCY * 2))  // 半周期10ms = 10000μs
#define MIN_TRIGGER_DELAY_US 500          // 最小触发延迟500μs
#define MAX_TRIGGER_DELAY_US 9500         // 最大触发延迟9.5ms

// 改进的可控硅相位控制类
class ImprovedPhaseControl {
private:
    // 核心控制参数
    int target_power_percent = 0;         // 目标功率百分比(0-100)
    float current_trigger_angle = 0;      // 当前触发角度(0-180度)
    unsigned long last_zero_cross_time = 0; // 上次过零时间
    bool output_state = false;            // 当前输出状态
    
    // 软件过零检测
    unsigned long software_zero_cross_timer = 0;
    bool zero_cross_detected = false;
    
    // 功率平滑控制
    float power_smoothing_factor = 0.1f;  // 功率平滑因子(0.05-0.2)
    int power_buffer[10] = {0};           // 功率缓冲区
    int buffer_index = 0;
    
    // 线性化查找表 - 解决可控硅非线性问题
    float linearization_table[101];       // 0-100%功率对应的触发角度
    
    // PWM输出参数
    int pwm_channel = 5;                  // PWM通道
    int pwm_frequency = 100;              // 降低到100Hz，接近交流电频率
    int pwm_resolution = 12;              // 12位分辨率
    
public:
    ImprovedPhaseControl(int channel = 5);
    
    // 初始化相位控制
    void begin();
    
    // 设置目标功率(0-100%)
    void setPower(int power_percent);
    
    // 获取当前功率
    int getCurrentPower();
    
    // 主循环更新函数 - 必须在loop()中高频调用
    void update();
    
    // 软件过零检测
    void updateZeroCrossing();
    
    // 计算触发延迟
    unsigned long calculateTriggerDelay(float power_percent);
    
    // 功率线性化处理
    float linearizePower(int power_percent);
    
    // 平滑功率变化
    int smoothPowerTransition(int new_power);
    
    // 重置控制器
    void reset();
    
    // 设置平滑因子
    void setSmoothingFactor(float factor);
    
    // 调试信息
    void printDebugInfo();
    
private:
    // 初始化线性化查找表
    void initializeLinearizationTable();
    
    // 计算实际PWM值
    int calculatePWMValue();
};

// 功率稳定性监控类
class PowerStabilityMonitor {
private:
    float power_history[20] = {0};        // 功率历史记录
    int history_index = 0;
    float stability_threshold = 2.0f;     // 稳定性阈值
    
public:
    // 添加功率数据点
    void addPowerData(float power);
    
    // 检查功率稳定性
    bool isPowerStable();
    
    // 获取功率变化率
    float getPowerVariation();
    
    // 重置监控
    void reset();
};

#endif // IMPROVED_PHASE_CONTROL_H