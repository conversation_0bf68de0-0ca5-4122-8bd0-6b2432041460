// phase_control.h
// 针对无过零检测系统的相位控制方案
#ifndef PHASE_CONTROL_H
#define PHASE_CONTROL_H

#include <Arduino.h>

// 相位控制参数
#define AC_FREQUENCY 50        // 中国电网频率50Hz
#define AC_PERIOD_MS (1000/AC_FREQUENCY)  // 20ms一个周期
#define PHASE_RESOLUTION 100   // 相位分辨率（0-100%）
#define MIN_PHASE_DELAY 100    // 最小触发延迟（微秒）
#define MAX_PHASE_DELAY 9000   // 最大触发延迟（微秒）

// 无过零检测的相位控制类
class PhaseControl {
private:
    int current_power = 0;           // 当前功率百分比
    int target_power = 0;            // 目标功率百分比
    unsigned long last_cycle_time = 0; // 上一个周期时间
    unsigned long cycle_duration = AC_PERIOD_MS * 1000; // 周期时长（微秒）
    bool use_random_phase = true;    // 使用随机相位启动以避免固定模式
    
    // 平滑滤波
    float power_buffer[10] = {0};    // 功率值缓冲区
    int buffer_index = 0;
    int buffer_size = 10;
    
public:
    PhaseControl();
    
    // 设置目标功率（0-100%）
    void setPower(int power);
    
    // 获取当前功率
    int getCurrentPower();
    
    // 更新相位控制（需要在loop中调用）
    void update();
    
    // 获取当前应输出的PWM值（0-4095）
    int getPWMValue();
    
    // 重置相位控制
    void reset();
    
    // 启用/禁用随机相位
    void setRandomPhase(bool enable);
};

// 简单的移动平均滤波器
class MovingAverageFilter {
private:
    float* buffer;
    int size;
    int index;
    float sum;
    
public:
    MovingAverageFilter(int filter_size);
    ~MovingAverageFilter();
    
    float filter(float value);
    void reset();
};

// 功率稳定性控制器
class PowerStabilizer {
private:
    int last_power = -1;
    unsigned long stable_start_time = 0;
    const unsigned long STABILITY_TIMEOUT = 2000; // 2秒稳定性检测
    bool is_stable = false;
    
public:
    bool checkStability(int current_power);
    bool isStable();
    void reset();
};

#endif // PHASE_CONTROL_H