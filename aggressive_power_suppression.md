# 强力功率压制算法

## 📊 最新实测数据分析

| 设置% | 实测功率 | 功率比例 | 问题严重程度 |
|-------|----------|----------|--------------|
| 12%   | 700W     | 58%      | 🔴 严重超标 |
| 14%   | 800W     | 67%      | 🔴 严重超标 |
| 16%   | 1100W    | 92%      | 🔴 极度超标 |

**结论**：可控硅在10-20%区间功率增长极其激进，必须强力压制！

## 🛡️ 强力压制策略

### 分段控制算法
```cpp
// 0-10%：扩大死区，完全关闭
if (i < 10) {
    pwm_linearization_table[i] = 0.0f;
}
// 10-20%：极度压制，最大只到25%功率
else if (i <= 20) {
    float range = (i - 10.0f) / 10.0f;
    pwm_linearization_table[i] = range * 0.25f;
}
// 20-40%：缓慢增长，25%-50%
else if (i <= 40) {
    float range = (i - 20.0f) / 20.0f;
    pwm_linearization_table[i] = 0.25f + range * 0.25f;
}
// 40-70%：中等增长，50%-75%
else if (i <= 70) {
    float range = (i - 40.0f) / 30.0f;
    pwm_linearization_table[i] = 0.50f + range * 0.25f;
}
// 70-100%：最终到满功率，75%-100%
else {
    float range = (i - 70.0f) / 30.0f;
    pwm_linearization_table[i] = 0.75f + range * 0.25f;
}
```

### 预期效果对比

| 设置% | 旧实测 | 新目标功率 | 压制比例 |
|-------|--------|------------|----------|
| 10%   | ?      | 0W         | 完全死区 |
| 12%   | 700W   | 150W       | 压制78% |
| 14%   | 800W   | 210W       | 压制74% |
| 16%   | 1100W  | 270W       | 压制75% |
| 18%   | ?      | 330W       | 大幅压制 |
| 20%   | ?      | 300W       | 25%上限 |

## 🎯 关键改进点

### 1. 扩大死区
- **从0-9%扩大到0-10%**
- 确保低设置完全无输出

### 2. 极度压制10-20%区间
- **这是问题最严重的区间**
- 10个百分点只映射到25%功率
- 平均每1%设置只增加2.5%实际功率

### 3. 分段渐进增长
- **20-40%**：缓慢增长（25%-50%）
- **40-70%**：中等增长（50%-75%）
- **70-100%**：最终到满功率

## 🧪 测试验证

### 关键测试命令
```bash
# 强力压制验证
PID;TRIACTEST

# 重点测试问题区间
PID;HEATERTEST,12    # 目标150W（不是700W）
PID;HEATERTEST,14    # 目标210W（不是800W）
PID;HEATERTEST,16    # 目标270W（不是1100W）
PID;HEATERTEST,20    # 目标300W（25%功率上限）
PID;HEATERTEST,0     # 关闭
```

### 成功标志
- **12%功率降到150W以下**
- **16%功率不超过300W**
- **20%以下都在可控范围**

## 📈 理论依据

### 可控硅导通特性
1. **触发阈值**：约10%PWM
2. **快速饱和区**：10-20%快速增长
3. **需要强力压制**：防止过早饱和

### 压制策略
1. **死区扩大**：避免意外触发
2. **非线性映射**：压制快速增长区
3. **分段控制**：不同区间不同策略

## 🔧 后续调优

如果效果仍不理想：

### 进一步压制
```cpp
// 如果12%仍然太强，进一步压制
pwm_linearization_table[i] = range * 0.15f;  // 改为15%上限
```

### 扩大死区
```cpp
// 如果需要更大死区
if (i < 12) {  // 扩大到12%
    pwm_linearization_table[i] = 0.0f;
}
```

### 更激进的压制
```cpp
// 10-30%区间都强力压制
else if (i <= 30) {
    float range = (i - 10.0f) / 20.0f;
    pwm_linearization_table[i] = range * 0.20f;  // 最大20%
}
```

现在测试强力压制算法，12%应该降到150W左右了！