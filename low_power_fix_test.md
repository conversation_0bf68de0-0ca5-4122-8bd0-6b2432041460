# 低功率区域修复测试指南

## 问题描述
4%火力设置导致加热器功率飙升，说明线性化算法在低功率区域有问题。

## 修复内容

### 1. 线性化算法修复
- **极低功率区域（0-10%）**：严格线性，轻微衰减（×0.8）
- **低功率区域（10-25%）**：轻微衰减（×0.9）
- **中功率区域（25-80%）**：接近线性（×1.0）
- **高功率区域（80-100%）**：轻微增强

### 2. 指数移动平均修复
- 修复了初始化问题，避免从0开始导致的异常值

### 3. 增强调试信息
- 显示详细的功率转换过程
- 添加PWM占空比显示

## 测试命令

### 基础测试
```bash
# 测试极低功率
PID;HEATERTEST,1     # 1%功率
PID;HEATERTEST,2     # 2%功率
PID;HEATERTEST,4     # 4%功率
PID;HEATERTEST,5     # 5%功率
PID;HEATERTEST,10    # 10%功率
PID;HEATERTEST,0     # 关闭

# 查看线性化查找表
PID;LINEARTABLE
```

### 预期结果
修复后，4%功率应该显示：
```
[OPTIMIZED_PWM] 功率设置: 4% -> 平滑: 4% -> 限制: 4%
[OPTIMIZED_PWM] 线性化: 0.032 -> PWM值: 131 (3.2%)
```

**关键指标**：
- 4%输入 → 约3.2%实际占空比
- PWM值应该在100-150范围内
- 不应该超过200（约5%占空比）

### 验证步骤

1. **上传修复后的固件**
2. **运行基础测试**，观察串口输出
3. **检查实际加热效果**，4%功率应该是很微弱的加热
4. **查看线性化表**：`PID;LINEARTABLE`

### 线性化表预期值
```
功率% | 线性化系数 | PWM值 | 占空比%
  0%  |    0.000   |    0  |    0.0%
  5%  |    0.040   |  164  |    4.0%
 10%  |    0.090   |  368  |    9.0%
 15%  |    0.135   |  553  |   13.5%
 20%  |    0.180   |  737  |   18.0%
```

## 如果仍有问题

### 进一步降低低功率区域
如果4%仍然太强，可以进一步调整：

```cpp
// 在 initializePWMLinearizationTable() 中
if (i < 10) {
    pwm_linearization_table[i] = linear_ratio * 0.6f;  // 更强的衰减
}
```

### 添加死区
如果需要更精确的低功率控制：

```cpp
if (i < 5) {
    pwm_linearization_table[i] = 0.0f;  // 5%以下完全关闭
} else if (i < 10) {
    pwm_linearization_table[i] = (linear_ratio - 0.05f) * 0.8f;
}
```

现在测试修复后的效果，4%功率应该是很温和的加热了！