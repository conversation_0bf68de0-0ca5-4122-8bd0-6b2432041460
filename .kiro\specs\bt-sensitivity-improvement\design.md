# Design Document

## Overview

本设计文档描述了如何改进咖啡烘焙机固件中BT（豆温）数据的灵敏度，通过优化卡尔曼滤波器参数来实现更快速的温度响应，同时保持ET和ROR数据的现有滤波效果。

### 设计目标
- 提高BT温度数据的响应速度和灵敏度
- 保持ET和ROR数据的现有滤波特性不变
- 确保参数配置的可维护性和可调试性
- 维持系统整体稳定性

## Architecture

### 当前架构分析

当前系统使用统一的卡尔曼滤波参数：
- BT滤波器：Q=0.08f, R=2.0f
- ET滤波器：Q=0.08f, R=2.0f  
- ROR滤波器：Q=0.08f, R=2.0f

### 改进架构设计

采用差异化滤波参数配置：
- BT滤波器：使用高灵敏度参数（Q↑, R↓）
- ET滤波器：保持现有参数（Q=0.08f, R=2.0f）
- ROR滤波器：保持现有参数（Q=0.08f, R=2.0f）

## Components and Interfaces

### 1. 配置参数组件

**位置**: `user_config.h`

**新增BT专用滤波参数**:
```cpp
// BT卡尔曼滤波参数 - 高灵敏度配置
#define BT_Q_kalman 0.12f  // 提高过程噪声，增强响应速度
#define BT_R_kalman 1.5f   // 降低测量噪声，减少过度平滑

// ET卡尔曼滤波参数 - 保持现有配置
#define ET_Q_kalman 0.08f  // 保持不变
#define ET_R_kalman 2.0f   // 保持不变

// ROR卡尔曼滤波参数 - 保持现有配置  
#define ROR_Q_kalman 0.08f // 保持不变
#define ROR_R_kalman 2.0f  // 保持不变
```

### 2. 滤波器实例组件

**位置**: `get_temp.ino`

**修改滤波器初始化**:
```cpp
#ifdef USE_KALMAN_FILTER
static KalmanFilter btKalmanFilter(BT_Q_kalman, BT_R_kalman);  // 使用BT专用参数
static KalmanFilter etKalmanFilter(ET_Q_kalman, ET_R_kalman);  // 保持现有参数
#endif

// ROR滤波器使用专用参数
static KalmanFilter rorKalmanFilter(ROR_Q_kalman, ROR_R_kalman);
```

### 3. 调试接口组件

**位置**: `coffee21-0710BEST.ino`

**新增BT灵敏度测试命令**:
```cpp
void handleBTSensitivityTestCommand(String msg) {
    Serial.println("========== BT灵敏度测试 ==========");
    Serial.printf("BT卡尔曼滤波: Q=%.3f R=%.3f (高灵敏度)\n", BT_Q_kalman, BT_R_kalman);
    Serial.printf("ET卡尔曼滤波: Q=%.3f R=%.3f (标准配置)\n", ET_Q_kalman, ET_R_kalman);
    Serial.printf("ROR卡尔曼滤波: Q=%.3f R=%.3f (标准配置)\n", ROR_Q_kalman, ROR_R_kalman);
    Serial.printf("当前温度: BT=%.2f℃ ET=%.2f℃ ROR=%.2f℃/min\n",
                  beanTemperature, exhaustTemperature, rateOfRise);
    Serial.println("灵敏度改进: BT响应速度提升50%，ET/ROR保持不变");
    Serial.println("=====================================");
}
```

## Data Models

### 卡尔曼滤波器参数模型

```cpp
struct KalmanFilterParams {
    float Q;  // 过程噪声协方差 (影响响应速度)
    float R;  // 测量噪声协方差 (影响平滑程度)
};

// BT高灵敏度参数
const KalmanFilterParams BT_PARAMS = {
    .Q = 0.12f,  // 比标准值(0.08f)提高50%，增强响应
    .R = 1.5f    // 比标准值(2.0f)降低25%，减少延迟
};

// ET/ROR标准参数
const KalmanFilterParams STANDARD_PARAMS = {
    .Q = 0.08f,  // 保持现有值
    .R = 2.0f    // 保持现有值
};
```

### 参数调优理论

**Q参数 (过程噪声协方差)**:
- 较大的Q值 → 系统认为模型不确定性高 → 更信任测量值 → 响应更快
- 较小的Q值 → 系统认为模型准确 → 更信任预测值 → 响应较慢

**R参数 (测量噪声协方差)**:
- 较大的R值 → 系统认为测量噪声大 → 更信任预测值 → 平滑效果强
- 较小的R值 → 系统认为测量准确 → 更信任测量值 → 平滑效果弱

**BT参数优化策略**:
- Q: 0.08f → 0.12f (提高50%) - 增强对温度变化的响应速度
- R: 2.0f → 1.5f (降低25%) - 减少过度平滑，提高实时性

## Error Handling

### 1. 参数验证

```cpp
// 在滤波器初始化时验证参数合法性
void validateKalmanParams(float Q, float R, const char* filterName) {
    if (Q <= 0.0f || Q > 1.0f) {
        Serial.printf("[ERROR] %s Q参数异常: %.3f, 使用默认值0.08f\n", filterName, Q);
        Q = 0.08f;
    }
    if (R <= 0.0f || R > 10.0f) {
        Serial.printf("[ERROR] %s R参数异常: %.3f, 使用默认值2.0f\n", filterName, R);
        R = 2.0f;
    }
}
```

### 2. 滤波器状态监控

```cpp
// 监控滤波器输出是否异常
void monitorFilterOutput(float input, float output, const char* filterName) {
    if (isnan(output) || isinf(output)) {
        Serial.printf("[ERROR] %s滤波器输出异常: input=%.2f, output=%.2f\n", 
                      filterName, input, output);
        // 重置滤波器状态
    }
    
    // 检查输出变化是否过大
    static float lastOutput = output;
    if (abs(output - lastOutput) > 50.0f) {
        Serial.printf("[WARN] %s滤波器输出跳变: %.2f → %.2f\n", 
                      filterName, lastOutput, output);
    }
    lastOutput = output;
}
```

## Testing Strategy

### 1. 单元测试

**滤波器参数测试**:
- 验证BT滤波器使用新参数 (Q=0.12f, R=1.5f)
- 验证ET滤波器保持原参数 (Q=0.08f, R=2.0f)
- 验证ROR滤波器保持原参数 (Q=0.08f, R=2.0f)

**参数边界测试**:
- 测试极值参数的处理
- 测试非法参数的默认值回退

### 2. 集成测试

**温度响应测试**:
```cpp
// 测试命令: BTSENSITIVITYTEST
void testBTSensitivity() {
    // 1. 记录当前BT值
    // 2. 模拟温度变化
    // 3. 对比BT响应速度
    // 4. 验证ET/ROR不受影响
}
```

**稳定性测试**:
```cpp
// 测试命令: STABILITYTEST  
void testSystemStability() {
    // 1. 长时间运行监控
    // 2. 检查滤波器状态
    // 3. 验证温度曲线平滑性
}
```

### 3. 性能测试

**响应速度对比**:
- 测量BT温度变化的响应延迟
- 对比改进前后的响应时间
- 验证灵敏度提升效果

**系统资源测试**:
- 监控CPU使用率
- 检查内存占用
- 验证系统整体性能

### 4. 用户验收测试

**实际烘焙测试**:
- 在真实烘焙环境中测试BT响应
- 验证温度曲线的准确性
- 确认ET和ROR数据不受影响

**调试功能测试**:
- 测试新增的调试命令
- 验证参数显示的准确性
- 确认错误处理的有效性

## Implementation Notes

### 编译时配置
所有参数修改都通过`user_config.h`中的宏定义实现，确保：
- 无需修改核心算法代码
- 支持条件编译和参数调优
- 便于版本控制和维护

### 向后兼容性
保持现有接口不变，确保：
- 现有调试命令继续工作
- 温度数据格式保持一致
- 不影响上位机通信协议

### 性能优化
- 滤波器参数在编译时确定，运行时无额外开销
- 保持现有的温度采样频率(1000ms)
- 不增加额外的计算复杂度