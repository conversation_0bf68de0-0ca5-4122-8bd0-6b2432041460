// Test file to verify filter validation implementation
// This file demonstrates the parameter validation and error handling functionality

#include "user_config.h"
#include "get_temp.h"
#include "filter_validation.h"
#include <Arduino.h>

void runFilterValidationTests() {
    Serial.println("========== 滤波器参数验证和错误处理测试 ==========");
    
    // Test 1: Parameter validation with normal values
    Serial.println("\n测试1: 正常参数验证");
    bool result1 = validateKalmanParams(0.08f, 2.0f, "TEST_NORMAL");
    Serial.printf("正常参数验证结果: %s\n", result1 ? "通过" : "失败");
    
    // Test 2: Parameter validation with abnormal values
    Serial.println("\n测试2: 异常参数验证");
    bool result2a = validateKalmanParams(-0.1f, 2.0f, "TEST_NEGATIVE_Q");
    bool result2b = validateKalmanParams(2.0f, 2.0f, "TEST_HIGH_Q");
    bool result2c = validateKalmanParams(0.08f, -1.0f, "TEST_NEGATIVE_R");
    bool result2d = validateKalmanParams(0.08f, 15.0f, "TEST_HIGH_R");
    Serial.printf("异常参数验证结果: Q负值=%s, Q过大=%s, R负值=%s, R过大=%s\n", 
                  result2a ? "通过" : "失败", result2b ? "通过" : "失败",
                  result2c ? "通过" : "失败", result2d ? "通过" : "失败");
    
    // Test 3: Safe parameter fallback mechanism
    Serial.println("\n测试3: 参数回退机制");
    float safeQ, safeR;
    getSafeKalmanParams(-0.1f, 15.0f, &safeQ, &safeR, "TEST_FALLBACK");
    Serial.printf("回退后参数: Q=%.3f (期望%.3f), R=%.1f (期望%.1f)\n", 
                  safeQ, DEFAULT_Q_VALUE, safeR, DEFAULT_R_VALUE);
    bool result3 = (safeQ == DEFAULT_Q_VALUE && safeR == DEFAULT_R_VALUE);
    Serial.printf("参数回退测试结果: %s\n", result3 ? "通过" : "失败");
    
    // Test 4: Filter initialization
    Serial.println("\n测试4: 滤波器初始化");
    initializeFiltersWithValidation();
    
    // Test 5: Filter instance creation
    Serial.println("\n测试5: 滤波器实例创建");
    KalmanFilter* btFilter = getBTKalmanFilter();
    KalmanFilter* etFilter = getETKalmanFilter();
    KalmanFilter* rorFilter = getRORKalmanFilter();
    
    bool result5 = (btFilter != nullptr && etFilter != nullptr && rorFilter != nullptr);
    Serial.printf("滤波器实例创建结果: %s\n", result5 ? "通过" : "失败");
    
    if (result5) {
        // Test 6: Filter output monitoring
        Serial.println("\n测试6: 滤波器输出监控");
        
        // Test normal output
        float testInput = 100.0f;
        float testOutput = btFilter->update(testInput);
        monitorFilterOutput(testInput, testOutput, "BT");
        
        // Test abnormal outputs
        Serial.println("测试异常输出监控:");
        monitorFilterOutput(100.0f, NAN, "TEST_NAN");
        monitorFilterOutput(100.0f, 500.0f, "TEST_RANGE");
        
        // Test jump detection
        monitorFilterOutput(100.0f, 50.0f, "TEST_BASELINE");
        monitorFilterOutput(100.0f, 200.0f, "TEST_JUMP");
    }
    
    // Summary
    Serial.println("\n========== 测试总结 ==========");
#ifdef ENABLE_FILTER_VALIDATION
    Serial.println("参数验证功能: 已启用");
#else
    Serial.println("参数验证功能: 未启用");
#endif

#ifdef ENABLE_FILTER_MONITORING
    Serial.println("输出监控功能: 已启用");
#else
    Serial.println("输出监控功能: 未启用");
#endif

    Serial.printf("参数范围: Q[%.3f-%.3f], R[%.1f-%.1f]\n", 
                  MIN_Q_VALUE, MAX_Q_VALUE, MIN_R_VALUE, MAX_R_VALUE);
    Serial.printf("默认参数: Q=%.3f, R=%.1f\n", DEFAULT_Q_VALUE, DEFAULT_R_VALUE);
    Serial.println("========== 测试完成 ==========");
}