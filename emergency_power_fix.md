# 紧急功率线性度修复

## 🚨 严重问题
- 18%设置 → 1100W实际功率（应该是216W）
- 线性度完全错误，存在安全风险

## ✅ 紧急修复方案

### 1. 完全线性化
移除了所有"优化"算法，采用严格的1:1线性映射：
```cpp
pwm_linearization_table[i] = i / 100.0f;  // 严格线性
```

### 2. 简化平滑算法
- 移除复杂的15点移动平均
- 改为简单的3点移动平均
- 避免过度平滑影响准确性

### 3. 禁用变化率限制
- 临时禁用功率变化率限制
- 确保设置值直接生效

## 🧪 验证测试

### 关键测试命令
```bash
# 功率准确性验证
PID;POWERVERIFY

# 手动测试关键点
PID;HEATERTEST,1     # 应该是12W
PID;HEATERTEST,5     # 应该是60W  
PID;HEATERTEST,10    # 应该是120W
PID;HEATERTEST,18    # 应该是216W（不是1100W！）
PID;HEATERTEST,25    # 应该是300W
PID;HEATERTEST,0     # 关闭
```

### 预期结果（1200W发热丝）
```
 1% -> 系数:0.010 -> PWM:  41 -> 预期功率:  12W
 5% -> 系数:0.050 -> PWM: 205 -> 预期功率:  60W
10% -> 系数:0.100 -> PWM: 410 -> 预期功率: 120W
15% -> 系数:0.150 -> PWM: 614 -> 预期功率: 180W
18% -> 系数:0.180 -> PWM: 737 -> 预期功率: 216W  ← 关键！
20% -> 系数:0.200 -> PWM: 819 -> 预期功率: 240W
25% -> 系数:0.250 -> PWM:1024 -> 预期功率: 300W
```

## 🔍 问题诊断

如果修复后仍不准确，可能的原因：

### 1. 硬件问题
- 光耦驱动电流不足
- 可控硅型号不匹配
- 散热不良导致特性变化

### 2. PWM频率问题
- 10kHz可能不适合你的可控硅
- 尝试调整频率：`PID;PWMFREQ,5000`

### 3. 电网电压影响
- 检查实际电网电压是否为220V
- 电压波动会影响功率计算

## 🛡️ 安全建议

### 立即措施
1. **限制最大功率**：在测试期间不要超过25%
2. **监控温度**：密切观察加热器温度
3. **准备断电**：随时准备切断电源

### 测试步骤
1. 从1%开始测试
2. 逐步增加到5%、10%、15%
3. 仔细观察实际加热效果
4. 如果18%仍然过强，立即停止测试

## 📊 功率对照表（1200W发热丝）

| 设置% | 预期功率 | PWM值 | 占空比% |
|-------|----------|-------|---------|
| 1%    | 12W      | 41    | 1.0%    |
| 5%    | 60W      | 205   | 5.0%    |
| 10%   | 120W     | 410   | 10.0%   |
| 15%   | 180W     | 614   | 15.0%   |
| 18%   | 216W     | 737   | 18.0%   |
| 20%   | 240W     | 819   | 20.0%   |
| 25%   | 300W     | 1024  | 25.0%   |

现在上传固件并小心测试。18%应该是温和的加热，不是强烈的！