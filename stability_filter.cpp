// stability_filter.cpp
// 增强稳定性滤波器实现
#include "stability_filter.h"
#include <Arduino.h>

EnhancedStabilityFilter::EnhancedStabilityFilter(int size) {
    history_size = size;
    history = new float[size];
    index = 0;
    last_stable_value = 0;
    last_change_time = 0;
    is_stable = false;
    
    for (int i = 0; i < size; i++) {
        history[i] = 0;
    }
}

EnhancedStabilityFilter::~EnhancedStabilityFilter() {
    delete[] history;
}

float EnhancedStabilityFilter::filter(float value) {
    // 添加到历史记录
    history[index] = value;
    index = (index + 1) % history_size;
    
    // 计算平均值和标准差
    float sum = 0;
    for (int i = 0; i < history_size; i++) {
        sum += history[i];
    }
    float avg = sum / history_size;
    
    // 计算标准差
    float variance = 0;
    for (int i = 0; i < history_size; i++) {
        float diff = history[i] - avg;
        variance += diff * diff;
    }
    variance /= history_size;
    float std_dev = sqrt(variance);
    
    // 检查稳定性
    if (std_dev < STABILITY_THRESHOLD) {
        if (!is_stable) {
            if (millis() - last_change_time >= STABILITY_TIMEOUT) {
                is_stable = true;
                last_stable_value = avg;
            }
        } else {
            // 检查是否超出死区
            if (abs(avg - last_stable_value) > DEAD_ZONE) {
                is_stable = false;
                last_change_time = millis();
            }
        }
    } else {
        is_stable = false;
        last_change_time = millis();
    }
    
    return is_stable ? last_stable_value : avg;
}

bool EnhancedStabilityFilter::isStable() {
    return is_stable;
}

void EnhancedStabilityFilter::reset() {
    index = 0;
    last_stable_value = 0;
    last_change_time = 0;
    is_stable = false;
    
    for (int i = 0; i < history_size; i++) {
        history[i] = 0;
    }
}

float EnhancedStabilityFilter::getStableValue() {
    return last_stable_value;
}

void EnhancedStabilityFilter::setStabilityThreshold(float threshold) {
    // 注意：这里修改的是局部变量，实际应该修改成员变量
    // 由于const限制，这里只是接口预留
}

void EnhancedStabilityFilter::setDeadZone(float dead_zone) {
    // 同上，接口预留
}

void EnhancedStabilityFilter::setStabilityTimeout(unsigned long timeout) {
    // 同上，接口预留
}

// 自适应死区控制器实现
AdaptiveDeadZone::AdaptiveDeadZone(float initial_dead_zone) {
    dead_zone = initial_dead_zone;
    last_value = 0;
    noise_level = 0;
    first_run = true;
}

float AdaptiveDeadZone::apply(float value) {
    if (first_run) {
        first_run = false;
        last_value = value;
        return value;
    }
    
    float diff = abs(value - last_value);
    
    // 如果变化在死区内，保持原值
    if (diff <= dead_zone) {
        return last_value;
    }
    
    // 更新噪声水平
    noise_level = noise_level * 0.9 + diff * 0.1;
    
    // 动态调整死区
    dead_zone = max(2.0f, min(10.0f, noise_level * 0.5f));
    
    last_value = value;
    return value;
}

void AdaptiveDeadZone::updateNoiseLevel(float new_value) {
    noise_level = new_value;
}

void AdaptiveDeadZone::setDeadZone(float new_dead_zone) {
    dead_zone = max(1.0f, min(15.0f, new_dead_zone));
}

float AdaptiveDeadZone::getDeadZone() {
    return dead_zone;
}

// 功率波动抑制器实现
PowerFluctuationSuppressor::PowerFluctuationSuppressor() {
    last_output = 0;
    stability_counter = 0;
}

float PowerFluctuationSuppressor::suppress(float input) {
    if (abs(input - last_output) < 0.5f) {
        stability_counter++;
        if (stability_counter >= STABILITY_COUNT) {
            last_output = input;
            stability_counter = 0;
        }
    } else {
        stability_counter = 0;
        last_output = input;
    }
    
    return last_output;
}

void PowerFluctuationSuppressor::reset() {
    last_output = 0;
    stability_counter = 0;
}