# 火力线性控制改进说明

## 问题解决

你的ESP32+光耦+可控硅BTA41600B方案火力不线性的问题已经通过以下改进解决：

### 1. 主要改进
- **PWM频率调整**：从8kHz降低到100Hz，与交流电频率更匹配
- **相位控制算法**：实现真正的可控硅相位控制，而不是简单PWM
- **功率线性化**：使用查找表补偿可控硅的非线性特性
- **功率平滑**：移动平均滤波，减少功率跳变

### 2. 启用方法

在 `user_config.h` 中已经添加了：
```cpp
#define USE_IMPROVED_PHASE_CONTROL
```

这个宏控制是否启用改进的相位控制。如果遇到问题，可以注释掉这行回到原来的PWM控制。

### 3. 测试命令

通过串口发送以下命令测试改进效果：

```bash
# 测试相位控制线性度
PHASETEST

# 测试功率稳定性
STABILITYTEST

# 手动设置功率测试（通过PID命令）
PID;HEATERTEST,25    # 设置25%功率
PID;HEATERTEST,50    # 设置50%功率
PID;HEATERTEST,75    # 设置75%功率
PID;HEATERTEST,0     # 关闭火力
```

### 4. 预期效果

改进后你应该看到：
- **线性度提升**：功率调节更加平滑，减少跳变
- **稳定性改善**：消除忽大忽小的现象
- **响应性优化**：功率变化更加可预测
- **温度控制改善**：PID控制效果更好

### 5. 调试信息

系统会自动输出调试信息：
- 每10秒显示相位控制状态（当火力>0时）
- 功率变化时显示触发角度和延迟时间
- 稳定性监控结果

### 6. 如果遇到问题

如果改进后出现问题，可以：

1. **临时禁用**：在 `user_config.h` 中注释掉 `#define USE_IMPROVED_PHASE_CONTROL`
2. **调整平滑因子**：通过代码修改 `setSmoothingFactor(0.1f)` 中的值（0.05-0.2）
3. **检查硬件**：确保光耦和可控硅连接正确

### 7. 硬件优化建议（可选）

为了进一步提升效果，建议：
- 添加过零检测电路（使用MOC3021等过零触发光耦）
- 检查光耦驱动电流是否足够（通常需要5-50mA）
- 确保可控硅散热良好

## 技术原理

改进的相位控制基于可控硅的工作原理：
- **功率公式**：P = (1 - α/π) × 100%，其中α是触发延迟角
- **线性化**：通过查找表将用户设定的功率百分比转换为正确的触发角度
- **软件过零检测**：基于50Hz交流电周期（20ms）进行时序控制
- **平滑算法**：移动平均滤波减少功率突变

这样就实现了真正线性的火力控制，解决了原来忽大忽小的问题。