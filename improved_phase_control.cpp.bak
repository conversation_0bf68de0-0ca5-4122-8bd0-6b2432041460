// improved_phase_control.cpp
// 改进的可控硅相位控制实现
#include "improved_phase_control.h"
#include "user_config.h"  // 需要引用配置文件中的常量
#include <driver/ledc.h>
#include <math.h>

ImprovedPhaseControl::ImprovedPhaseControl(int channel) {
    pwm_channel = channel;
    initializeLinearizationTable();
}

void ImprovedPhaseControl::begin() {
    // 使用现有的LEDC配置，不重新初始化
    Serial.println("[PHASE_CONTROL] 改进的相位控制已初始化");
    Serial.printf("[PHASE_CONTROL] PWM频率: %dHz, 分辨率: %d位\n", pwm_frequency, pwm_resolution);
}

void ImprovedPhaseControl::initializeLinearizationTable() {
    // 创建线性化查找表，补偿可控硅的非线性特性
    for (int i = 0; i <= 100; i++) {
        if (i == 0) {
            linearization_table[i] = 180.0f;  // 0%功率 = 180度延迟(不导通)
        } else if (i == 100) {
            linearization_table[i] = 0.0f;    // 100%功率 = 0度延迟(全导通)
        } else {
            // 使用反余弦函数实现线性化
            // 功率 = (1 - α/π) × 100%，反推得到 α = π × (1 - P/100)
            float power_ratio = i / 100.0f;
            float angle_rad = M_PI * (1.0f - power_ratio);
            linearization_table[i] = angle_rad * 180.0f / M_PI;
            
            // 应用经验修正，改善低功率区域的线性度
            if (i < 20) {
                linearization_table[i] *= 0.95f;  // 低功率区域修正
            } else if (i > 80) {
                linearization_table[i] *= 1.05f;  // 高功率区域修正
            }
        }
    }
    
    Serial.println("[PHASE_CONTROL] 功率线性化查找表已初始化");
}

void ImprovedPhaseControl::setPower(int power_percent) {
    // 限制功率范围
    power_percent = constrain(power_percent, 0, 100);
    
    // 应用功率平滑
    int smoothed_power = smoothPowerTransition(power_percent);
    target_power_percent = smoothed_power;
    
    // 计算对应的触发角度
    current_trigger_angle = linearizePower(target_power_percent);
    
    Serial.printf("[PHASE_CONTROL] 设置功率: %d%% -> 平滑后: %d%% -> 触发角: %.1f度\n", 
                  power_percent, smoothed_power, current_trigger_angle);
}

int ImprovedPhaseControl::getCurrentPower() {
    return target_power_percent;
}

void ImprovedPhaseControl::update() {
    // 更新软件过零检测
    updateZeroCrossing();
    
    // 计算并输出PWM值
    int pwm_value = calculatePWMValue();
    ledcWriteChannel(pwm_channel, pwm_value);
}

void ImprovedPhaseControl::updateZeroCrossing() {
    unsigned long current_time = micros();
    
    // 软件过零检测 - 基于50Hz交流电周期
    if (current_time - software_zero_cross_timer >= AC_HALF_PERIOD_US) {
        software_zero_cross_timer = current_time;
        zero_cross_detected = true;
        last_zero_cross_time = current_time;
    } else {
        zero_cross_detected = false;
    }
}

unsigned long ImprovedPhaseControl::calculateTriggerDelay(float power_percent) {
    if (power_percent <= 0) {
        return AC_HALF_PERIOD_US;  // 不触发
    }
    if (power_percent >= 100) {
        return MIN_TRIGGER_DELAY_US;  // 立即触发
    }
    
    // 根据线性化角度计算延迟时间
    float angle = linearizePower(power_percent);
    float delay_ratio = angle / 180.0f;
    unsigned long delay_us = MIN_TRIGGER_DELAY_US + 
                            (unsigned long)(delay_ratio * (MAX_TRIGGER_DELAY_US - MIN_TRIGGER_DELAY_US));
    
    return constrain(delay_us, MIN_TRIGGER_DELAY_US, MAX_TRIGGER_DELAY_US);
}

float ImprovedPhaseControl::linearizePower(int power_percent) {
    power_percent = constrain(power_percent, 0, 100);
    return linearization_table[power_percent];
}

int ImprovedPhaseControl::smoothPowerTransition(int new_power) {
    // 将新功率值添加到缓冲区
    power_buffer[buffer_index] = new_power;
    buffer_index = (buffer_index + 1) % 10;
    
    // 计算移动平均
    int sum = 0;
    for (int i = 0; i < 10; i++) {
        sum += power_buffer[i];
    }
    
    return sum / 10;
}

int ImprovedPhaseControl::calculatePWMValue() {
    if (target_power_percent <= 0) {
        return 0;  // 0功率，输出低电平
    }
    
    if (target_power_percent >= 100) {
        return 4095;  // 100%功率，输出高电平
    }
    
    // 简化的线性映射，基于改进的线性化算法
    float linear_power = linearizePower(target_power_percent);
    float power_ratio = (180.0f - linear_power) / 180.0f;  // 反转角度到功率比
    
    return (int)(power_ratio * 4095);
}

void ImprovedPhaseControl::reset() {
    target_power_percent = 0;
    current_trigger_angle = 180.0f;
    last_zero_cross_time = 0;
    output_state = false;
    
    // 清空功率缓冲区
    for (int i = 0; i < 10; i++) {
        power_buffer[i] = 0;
    }
    buffer_index = 0;
    
    Serial.println("[PHASE_CONTROL] 相位控制器已重置");
}

void ImprovedPhaseControl::setSmoothingFactor(float factor) {
    power_smoothing_factor = constrain(factor, 0.01f, 0.5f);
    Serial.printf("[PHASE_CONTROL] 平滑因子设置为: %.3f\n", power_smoothing_factor);
}

void ImprovedPhaseControl::printDebugInfo() {
    Serial.println("========== 相位控制调试信息 ==========");
    Serial.printf("目标功率: %d%%\n", target_power_percent);
    Serial.printf("触发角度: %.1f度\n", current_trigger_angle);
    Serial.printf("触发延迟: %lu微秒\n", calculateTriggerDelay(target_power_percent));
    Serial.printf("PWM频率: %dHz\n", pwm_frequency);
    Serial.printf("平滑因子: %.3f\n", power_smoothing_factor);
    Serial.println("=====================================");
}

// PowerStabilityMonitor 实现
void PowerStabilityMonitor::addPowerData(float power) {
    power_history[history_index] = power;
    history_index = (history_index + 1) % 20;
}

bool PowerStabilityMonitor::isPowerStable() {
    float variation = getPowerVariation();
    return variation < stability_threshold;
}

float PowerStabilityMonitor::getPowerVariation() {
    float sum = 0;
    float mean = 0;
    
    // 计算平均值
    for (int i = 0; i < 20; i++) {
        sum += power_history[i];
    }
    mean = sum / 20.0f;
    
    // 计算标准差
    float variance = 0;
    for (int i = 0; i < 20; i++) {
        variance += pow(power_history[i] - mean, 2);
    }
    
    return sqrt(variance / 20.0f);
}

void PowerStabilityMonitor::reset() {
    for (int i = 0; i < 20; i++) {
        power_history[i] = 0;
    }
    history_index = 0;
}